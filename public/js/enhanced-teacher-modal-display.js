// Enhanced Teacher Modal Display Functions
// This file contains all the display and utility functions for the enhanced teacher modal

// Display enhanced teacher profile data in modal
function displayEnhancedTeacherProfile(teacher) {
    console.log('🎯 displayEnhancedTeacherProfile called with teacher:', teacher);
    console.log('🔍 ENHANCED DATA DEBUG:');
    console.log('- Teacher name:', teacher.name);
    console.log('- Certifications array:', teacher.certifications);
    console.log('- Certifications length:', teacher.certifications ? teacher.certifications.length : 'undefined');
    console.log('- Skills by category:', teacher.skillsByCategory);
    console.log('- Skills categories:', teacher.skillsByCategory ? Object.keys(teacher.skillsByCategory) : 'undefined');

    // Store teacher data globally for PDF generation
    window.currentTeacherData = teacher;
    console.log('✅ Teacher data stored globally for PDF generation');

    // Basic information (enhanced with user table data)
    $('#modal-teacher-name').text(teacher.displayName || teacher.fullName || teacher.full_name || teacher.name || 'Unknown Teacher');
    $('#modal-teacher-designation').text(teacher.designation || 'Teacher');
    $('#modal-teacher-department').text(teacher.department || 'Academic Department');
    $('#modal-teacher-employee-id').text(teacher.employee_id || 'N/A');
    $('#modal-teacher-email').text(teacher.primaryEmail || teacher.email || 'No email provided');
    $('#modal-teacher-phone').text(teacher.phone || 'No phone provided');
    $('#modal-teacher-joining-date').text(formatModalDate(teacher.joining_date) || 'Not specified');
    $('#modal-teacher-employment-type').text(capitalizeModalFirst(teacher.employment_type) || 'Not specified');

    // Enhanced personal details with user table integration
    $('#modal-date-of-birth').text(formatModalDate(teacher.dateOfBirth || teacher.date_of_birth) || 'Not provided');
    if (teacher.age) {
        $('#modal-date-of-birth').append(` (Age: ${teacher.age} years)`);
    }
    $('#modal-gender').text(capitalizeModalFirst(teacher.gender) || 'Not specified');
    $('#modal-subjects-taught').text(teacher.subjects_taught || teacher.subjects || 'Not specified');
    $('#modal-classes-handled').text(teacher.classes_handled || 'Not specified');

    // User account information
    $('#modal-account-status').text(teacher.accountStatus || 'Unknown');
    $('#modal-last-login').text(teacher.lastLoginFormatted || 'Never logged in');
    $('#modal-account-created').text(teacher.accountCreated || 'Unknown');
    $('#modal-username').text(teacher.username || 'Not set');

    // Experience stats
    $('#modal-total-experience').text(teacher.total_experience_years || '0');
    $('#modal-teaching-experience').text(teacher.teaching_experience_years || '0');
    $('#modal-administrative-experience').text(teacher.administrative_experience_years || '0');

    // Profile image
    if (teacher.profile_image && teacher.profile_image !== 'null') {
        $('#modal-profile-image').attr('src', teacher.profile_image).removeClass('hidden');
        $('#modal-profile-image-placeholder').addClass('hidden');
    } else {
        const initials = getModalInitials(teacher);
        $('#modal-profile-image-placeholder').text(initials).removeClass('hidden');
    }

    // Display contact and administrative information
    displayModalContactInfo(teacher);
    displayModalAdministrativeInfo(teacher);

    // Display timelines
    displayModalEducationTimeline(teacher.educationTimeline || []);
    displayModalExperienceTimeline(teacher.experienceTimeline || []);

    // Display publications and research
    displayModalPublicationsResearch(teacher);

    // Display enhanced certifications and skills (with fallback to legacy)
    const hasEnhancedCertifications = teacher.certifications && teacher.certifications.length > 0;
    const hasEnhancedSkills = teacher.skillsByCategory && Object.keys(teacher.skillsByCategory).length > 0;

    console.log('🔍 Enhanced data availability:');
    console.log('- Certifications:', hasEnhancedCertifications, teacher.certifications?.length || 0);
    console.log('- Skills:', hasEnhancedSkills, Object.keys(teacher.skillsByCategory || {}).length);

    // Debug alert to confirm this code is running
    if (hasEnhancedCertifications || hasEnhancedSkills) {
        console.log('🚨 ENHANCED DATA FOUND! Should display enhanced sections');
    }

    if (hasEnhancedCertifications) {
        console.log('✅ Using enhanced certifications');
        displayModalEnhancedCertifications(teacher.certifications);
    } else {
        console.log('⚠️ Falling back to legacy certifications');
        displayModalCertifications(teacher);
    }

    if (hasEnhancedSkills) {
        console.log('✅ Using enhanced skills');
        displayModalEnhancedSkills(teacher.skillsByCategory);
    } else {
        console.log('⚠️ Falling back to legacy skills');
        displayModalSkillsAndLanguages(teacher.special_skills, teacher.languages_known);
    }

    // Display achievements - check for enhanced data first
    const hasEnhancedAchievements = teacher.achievementsByCategory && Object.keys(teacher.achievementsByCategory).length > 0;

    console.log('🔍 Enhanced achievements availability:');
    console.log('- Achievements:', hasEnhancedAchievements, Object.keys(teacher.achievementsByCategory || {}).length);

    if (hasEnhancedAchievements) {
        console.log('✅ Using enhanced achievements');
        displayModalEnhancedAchievements(teacher.achievementsByCategory);
    } else {
        console.log('⚠️ Falling back to legacy achievements');
        displayModalAchievements(teacher.awards_received, teacher.training_programs);
    }

    // Display notes and previous organizations
    displayModalNotesAndOrganizations(teacher);

    // Calculate and display profile completion
    console.log('🔍 Teacher data received:', teacher);
    console.log('🔍 Available fields:', Object.keys(teacher));

    // Try to access functions from window object
    const calcFunction = window.calculateProfileCompletion || calculateProfileCompletion;
    const updateFunction = window.updateProfileCompletionDisplay || updateProfileCompletionDisplay;

    if (typeof calcFunction === 'function' && typeof updateFunction === 'function') {
        const completionData = calcFunction(teacher);
        updateFunction(completionData);
        console.log('✅ Profile completion calculated:', completionData);
    } else {
        console.log('⚠️ Profile completion functions not available');
        console.log('calcFunction type:', typeof calcFunction);
        console.log('updateFunction type:', typeof updateFunction);
        console.log('window.calculateProfileCompletion:', typeof window.calculateProfileCompletion);

        // Manual calculation as fallback
        console.log('🔄 Attempting manual profile completion calculation...');
        manualProfileCompletionCalculation(teacher);
    }
}

// Manual profile completion calculation as fallback
function manualProfileCompletionCalculation(teacherData) {
    console.log('🔄 Manual profile completion calculation started');

    if (!teacherData) {
        console.log('❌ No teacher data provided');
        return;
    }

    let totalFields = 0;
    let completedFields = 0;

    // Helper function to check if field has meaningful value
    function hasValue(value) {
        return value !== null &&
               value !== undefined &&
               value !== 'null' &&
               value !== 'undefined' &&
               value.toString().trim() !== '' &&
               value.toString().trim() !== '0' &&
               value.toString().trim() !== 'Not provided';
    }

    // Basic user information from users table (8 fields)
    const basicFields = [
        'name', 'email', 'full_name', 'username', 'date_of_birth',
        'bio', 'profile_image', 'subjects'
    ];

    console.log('🔍 Checking basic fields:');
    basicFields.forEach(field => {
        totalFields++;
        const value = teacherData[field];
        if (value && hasValue(value)) {
            completedFields++;
            console.log(`✅ Basic field ${field}: "${value}"`);
        } else {
            console.log(`❌ Basic field ${field}: "${value}" (empty or invalid)`);
        }
    });

    // Professional information from staff table (12 fields)
    const professionalFields = [
        'employee_id', 'designation', 'department', 'joining_date',
        'employment_type', 'phone', 'emergency_contact', 'address',
        'office_location', 'current_salary', 'subjects_taught', 'languages_known'
    ];

    console.log('🔍 Checking professional fields:');
    professionalFields.forEach(field => {
        totalFields++;
        const value = teacherData[field];
        if (value && hasValue(value)) {
            completedFields++;
            console.log(`✅ Professional field ${field}: "${value}"`);
        } else {
            console.log(`❌ Professional field ${field}: "${value}" (empty or invalid)`);
        }
    });

    // Educational qualifications from staff table (9 fields)
    const educationFields = [
        'class_10_board', 'class_10_year', 'class_10_percentage',
        'class_12_board', 'class_12_year', 'class_12_percentage',
        'graduation_degree', 'graduation_university', 'graduation_year'
    ];

    educationFields.forEach(field => {
        totalFields++;
        if (teacherData[field] && hasValue(teacherData[field])) {
            completedFields++;
            console.log(`✅ Education field ${field}: ${teacherData[field]}`);
        } else {
            console.log(`❌ Education field ${field}: empty or invalid`);
        }
    });

    // Experience and additional fields from staff table (6 fields)
    const experienceFields = [
        'total_experience_years', 'teaching_experience_years', 'previous_organizations',
        'special_skills', 'professional_certifications', 'performance_rating'
    ];

    experienceFields.forEach(field => {
        totalFields++;
        if (teacherData[field] && hasValue(teacherData[field])) {
            completedFields++;
            console.log(`✅ Experience field ${field}: ${teacherData[field]}`);
        } else {
            console.log(`❌ Experience field ${field}: empty or invalid`);
        }
    });

    const percentage = totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;

    const completionData = {
        percentage: percentage,
        completedFields: completedFields,
        totalFields: totalFields,
        missingFields: totalFields - completedFields
    };

    console.log('📊 Manual calculation result:', completionData);

    // Update the display manually
    $('#summary-profile-completion-percentage').text(percentage + '%');
    $('#total-fields-count').text(totalFields);
    $('#completed-fields-count').text(completedFields);

    // Calculate basic and professional separately
    let basicCompleted = 0;
    basicFields.forEach(field => {
        if (teacherData[field] && hasValue(teacherData[field])) {
            basicCompleted++;
        }
    });

    let professionalCompleted = 0;
    professionalFields.forEach(field => {
        if (teacherData[field] && hasValue(teacherData[field])) {
            professionalCompleted++;
        }
    });

    $('#basic-info-completion').text(`${basicCompleted}/${basicFields.length}`);
    $('#professional-completion').text(`${professionalCompleted}/${professionalFields.length}`);

    console.log('✅ Manual profile completion display updated');

    return completionData;
}

// Display education timeline in modal (horizontal layout)
function displayModalEducationTimeline(timeline) {
    const container = $('#modal-education-timeline');
    container.empty();

    if (!timeline || timeline.length === 0) {
        container.html('<p class="text-gray-500 text-center">No educational information available</p>');
        return;
    }

    timeline.forEach((item, index) => {
        // Build subjects display if available
        let subjectsHtml = '';
        if (item.subjects) {
            subjectsHtml = '<div class="mt-2"><p class="text-xs text-green-600 font-medium mb-1">Subjects:</p>';
            Object.entries(item.subjects).forEach(([subject, marks]) => {
                subjectsHtml += `<div class="text-xs text-green-500">• ${subject}: ${marks.marks}/${marks.total}</div>`;
            });
            subjectsHtml += '</div>';
        }

        // Build performance display
        let performanceHtml = '';
        if (item.percentage) {
            performanceHtml += `<p class="text-xs text-green-600 mb-1">📊 ${item.percentage}%`;
            if (item.totalMarks && item.maxMarks) {
                performanceHtml += ` (${item.totalMarks}/${item.maxMarks})`;
            }
            performanceHtml += '</p>';
        }
        if (item.grade) {
            performanceHtml += `<p class="text-xs text-green-600 mb-1">🏆 Grade: ${item.grade}</p>`;
        }
        if (item.cgpa) {
            performanceHtml += `<p class="text-xs text-green-600 mb-1">📈 CGPA: ${item.cgpa}</p>`;
        }

        const timelineItem = $(`
            <div class="relative flex-shrink-0 w-72">
                <div class="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg p-4 h-full">
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 bg-green-600 rounded-full border-2 border-white shadow-lg mr-2"></div>
                        <h5 class="font-bold text-sm text-green-800">${item.title}</h5>
                    </div>
                    <p class="text-green-700 font-medium text-sm mb-2">🏫 ${item.institution}</p>
                    ${item.board ? `<p class="text-xs text-green-600 mb-1">📋 Board: ${item.board}</p>` : ''}
                    ${item.specialization ? `<p class="text-xs text-green-600 mb-1">🎯 ${item.specialization}</p>` : ''}
                    ${performanceHtml}
                    ${item.achievements ? `<p class="text-xs text-green-600 mb-1">🏅 ${item.achievements}</p>` : ''}
                    ${item.thesis ? `<p class="text-xs text-green-600 mb-1">📝 Thesis: ${item.thesis}</p>` : ''}
                    ${subjectsHtml}
                    <p class="text-xs text-gray-600 mt-2">
                        <i class="fas fa-calendar mr-1"></i>${item.year}
                    </p>
                </div>
            </div>
        `);

        container.append(timelineItem);
    });
}

// Display experience timeline in modal (horizontal layout - combined professional experience)
function displayModalExperienceTimeline(timeline) {
    console.log('displayModalExperienceTimeline called with:', timeline);
    console.log('Timeline length:', timeline ? timeline.length : 'undefined');
    const container = $('#modal-experience-timeline');
    container.empty();

    if (!timeline || timeline.length === 0) {
        console.log('No experience timeline data - showing empty message');
        container.html('<p class="text-gray-500 text-center">No experience information available</p>');
        return;
    }

    console.log('Processing', timeline.length, 'experience records');

    // Sort timeline by start date to show chronological order
    const sortedTimeline = timeline.sort((a, b) => {
        if (a.year && b.year) return a.year - b.year;
        return 0;
    });

    sortedTimeline.forEach((item, index) => {
        // Determine if this is a previous experience (not current)
        const isPrevious = !item.isCurrent;

        // Build responsibilities display
        let responsibilitiesHtml = '';
        if (item.responsibilities && item.responsibilities.length > 0) {
            responsibilitiesHtml = '<div class="mt-2"><p class="text-xs font-medium mb-1">Key Responsibilities:</p>';
            item.responsibilities.slice(0, 3).forEach(resp => {
                responsibilitiesHtml += `<div class="text-xs mb-1">• ${resp}</div>`;
            });
            if (item.responsibilities.length > 3) {
                responsibilitiesHtml += `<div class="text-xs">... and ${item.responsibilities.length - 3} more</div>`;
            }
            responsibilitiesHtml += '</div>';
        }

        // Build achievements display
        let achievementsHtml = '';
        if (item.achievements && item.achievements.length > 0) {
            achievementsHtml = '<div class="mt-2"><p class="text-xs font-medium mb-1">🏆 Key Achievements:</p>';
            item.achievements.slice(0, 2).forEach(achievement => {
                achievementsHtml += `<div class="text-xs mb-1">• ${achievement}</div>`;
            });
            if (item.achievements.length > 2) {
                achievementsHtml += `<div class="text-xs">... and ${item.achievements.length - 2} more</div>`;
            }
            achievementsHtml += '</div>';
        }

        // Build skills display
        let skillsHtml = '';
        if (item.skills && item.skills.length > 0) {
            skillsHtml = `<div class="mt-2"><p class="text-xs font-medium mb-1">🛠️ Skills:</p>`;
            skillsHtml += `<div class="text-xs">${item.skills.slice(0, 4).join(', ')}`;
            if (item.skills.length > 4) {
                skillsHtml += ` +${item.skills.length - 4} more`;
            }
            skillsHtml += '</div></div>';
        }

        const timelineItem = $(`
            <div class="relative flex-shrink-0 w-80">
                <div class="bg-gradient-to-r ${item.isCurrent ? 'from-purple-50 to-purple-100 border-purple-200' : 'from-gray-50 to-gray-100 border-gray-300'} border rounded-lg p-4 h-full ${isPrevious ? 'opacity-90' : ''}">
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 ${item.isCurrent ? 'bg-purple-600' : 'bg-gray-500'} rounded-full border-2 border-white shadow-lg mr-2"></div>
                        <h5 class="font-bold text-sm ${item.isCurrent ? 'text-purple-800' : 'text-gray-700'}">${item.title}</h5>
                        ${isPrevious ? '<span class="ml-auto text-xs text-gray-500 font-medium">Previous</span>' : ''}
                    </div>
                    <p class="${item.isCurrent ? 'text-purple-700' : 'text-gray-600'} font-medium text-sm mb-2">🏢 ${item.institution}</p>
                    <p class="text-xs ${item.isCurrent ? 'text-purple-600' : 'text-gray-500'} mb-2">
                        <i class="fas fa-calendar mr-1"></i>${item.duration || item.year}
                    </p>
                    ${item.description ? `<p class="text-xs ${item.isCurrent ? 'text-purple-600' : 'text-gray-500'} mb-2 leading-relaxed">📝 ${item.description}</p>` : ''}
                    ${item.performanceRating ? `<p class="text-xs ${item.isCurrent ? 'text-purple-600' : 'text-gray-500'} mb-1">⭐ Performance: ${item.performanceRating}</p>` : ''}
                    ${responsibilitiesHtml}
                    ${achievementsHtml}
                    ${skillsHtml}
                    <div class="mt-3">
                        ${item.isCurrent ? '<span class="inline-block bg-purple-200 text-purple-800 text-xs px-2 py-1 rounded-full">Current Position</span>' : '<span class="inline-block bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">Previous Experience</span>'}
                    </div>
                </div>
            </div>
        `);

        container.append(timelineItem);
    });
}

// Display contact information in modal
function displayModalContactInfo(teacher) {
    $('#modal-alternate-phone').text(teacher.alternate_phone || 'Not provided');
    $('#modal-emergency-contact').text(teacher.emergency_contact || 'Not provided');
    $('#modal-address').text(teacher.address || 'Not provided');
    $('#modal-city').text(teacher.city || 'Not provided');
    $('#modal-state').text(teacher.state || 'Not provided');
    $('#modal-pincode').text(teacher.pincode || 'Not provided');
}

// Display administrative information in modal
function displayModalAdministrativeInfo(teacher) {
    $('#modal-office-location').text(teacher.office_location || 'Not specified');
    $('#modal-confirmation-date').text(formatModalDate(teacher.confirmation_date) || 'Not specified');
    $('#modal-last-promotion').text(formatModalDate(teacher.last_promotion_date) || 'Not specified');
    $('#modal-performance-rating').text(capitalizeModalFirst(teacher.performance_rating) || 'Not rated');
    $('#modal-current-salary').text(teacher.current_salary ? `₹${teacher.current_salary.toLocaleString()}` : 'Not specified');
}

// Display publications and research in modal
function displayModalPublicationsResearch(teacher) {
    // Publications
    const publicationsContainer = $('#modal-publications-list');
    if (teacher.publications) {
        const publicationsArray = teacher.publications.split(',');
        const publicationsHtml = publicationsArray.map(publication =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-file-alt text-gray-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${publication.trim()}</span>
            </div>`
        ).join('');
        publicationsContainer.html(publicationsHtml);
    } else {
        publicationsContainer.html('<p class="text-gray-500 text-xs">No publications available</p>');
    }

    // Research Papers
    const researchContainer = $('#modal-research-papers-list');
    if (teacher.research_papers) {
        const researchArray = teacher.research_papers.split(',');
        const researchHtml = researchArray.map(paper =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-microscope text-gray-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${paper.trim()}</span>
            </div>`
        ).join('');
        researchContainer.html(researchHtml);
    } else {
        researchContainer.html('<p class="text-gray-500 text-xs">No research papers available</p>');
    }

    // Conferences
    const conferencesContainer = $('#modal-conferences-list');
    if (teacher.conferences_attended) {
        const conferencesArray = teacher.conferences_attended.split(',');
        const conferencesHtml = conferencesArray.map(conference =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-users text-gray-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${conference.trim()}</span>
            </div>`
        ).join('');
        conferencesContainer.html(conferencesHtml);
    } else {
        conferencesContainer.html('<p class="text-gray-500 text-xs">No conferences attended</p>');
    }
}

// Display certifications in modal
function displayModalCertifications(teacher) {
    // Professional Certifications
    const certificationsContainer = $('#modal-certifications-list');
    if (teacher.professional_certifications) {
        const certificationsArray = teacher.professional_certifications.split(',');
        const certificationsHtml = certificationsArray.map(cert =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-award text-gray-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${cert.trim()}</span>
            </div>`
        ).join('');
        certificationsContainer.html(certificationsHtml);
    } else {
        certificationsContainer.html('<p class="text-gray-500 text-xs">No certifications available</p>');
    }

    // Other Qualifications
    const qualificationsContainer = $('#modal-other-qualifications-list');
    if (teacher.other_qualifications) {
        const qualificationsArray = teacher.other_qualifications.split(',');
        const qualificationsHtml = qualificationsArray.map(qual =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-graduation-cap text-gray-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${qual.trim()}</span>
            </div>`
        ).join('');
        qualificationsContainer.html(qualificationsHtml);
    } else {
        qualificationsContainer.html('<p class="text-gray-500 text-xs">No additional qualifications available</p>');
    }
}

// Display enhanced certifications from API
function displayModalEnhancedCertifications(certifications) {
    console.log('🔍 displayModalEnhancedCertifications called with:', certifications);
    console.log('Certifications length:', certifications ? certifications.length : 'undefined');

    // Use the existing modal certifications container
    const container = $('#modal-certifications-list');
    if (!container.length) {
        console.log('❌ Certifications container not found');
        return;
    }

    console.log('✅ Using certifications container:', container.length);
    displayEnhancedCertificationsInContainer(container, certifications);
}

function displayEnhancedCertificationsInContainer(container, certifications) {
    console.log('🎯 displayEnhancedCertificationsInContainer called');
    console.log('Container:', container.length);
    console.log('Certifications data:', certifications);

    container.empty();

    if (!certifications || certifications.length === 0) {
        console.log('❌ No certifications data available');
        container.html('<p class="text-gray-500 text-xs">No certifications available</p>');
        return;
    }

    console.log('✅ Processing', certifications.length, 'certifications');

    const certificationsHtml = certifications.map(cert => {
        console.log('Processing certification:', cert.name);
        const expiryText = cert.isLifetime ? 'Lifetime' : (cert.expiryDate ? new Date(cert.expiryDate).toLocaleDateString() : 'No expiry');
        const skillsText = cert.skillsCovered && cert.skillsCovered.length > 0 ? cert.skillsCovered.join(', ') : 'No skills listed';

        return `
            <div class="flex items-start space-x-3 mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <i class="fas fa-award text-gray-600 mt-1 text-sm"></i>
                <div class="flex-1">
                    <h6 class="font-semibold text-gray-800 text-sm">${cert.name}</h6>
                    <p class="text-xs text-gray-600 mb-1">🏢 ${cert.issuer}</p>
                    <p class="text-xs text-gray-600 mb-1">📅 Issued: ${new Date(cert.issueDate).toLocaleDateString()}</p>
                    <p class="text-xs text-gray-600 mb-1">⏰ Expires: ${expiryText}</p>
                    <p class="text-xs text-gray-600 mb-1">🆔 ID: ${cert.certificateId || 'Not provided'}</p>
                    <p class="text-xs text-gray-600 mb-1">✅ Status: ${cert.status}</p>
                    ${cert.description ? `<p class="text-xs text-gray-600 mb-2">📝 ${cert.description}</p>` : ''}
                    <p class="text-xs text-gray-600">🛠️ Skills: ${skillsText}</p>
                    <span class="inline-block mt-2 bg-gray-200 text-gray-800 text-xs px-2 py-1 rounded-full">${cert.type}</span>
                </div>
            </div>
        `;
    }).join('');

    console.log('✅ Setting certifications HTML');
    container.html(certificationsHtml);
}

// Display enhanced skills from API
function displayModalEnhancedSkills(skillsByCategory) {
    console.log('🔍 displayModalEnhancedSkills called with:', skillsByCategory);
    console.log('Skills categories:', skillsByCategory ? Object.keys(skillsByCategory) : 'undefined');

    // Use the existing modal skills container
    const container = $('#modal-skills-list');
    if (!container.length) {
        console.log('❌ Skills container not found');
        return;
    }

    console.log('✅ Using skills container:', container.length);
    displayEnhancedSkillsInContainer(container, skillsByCategory);
}

function displayEnhancedSkillsInContainer(container, skillsByCategory) {
    console.log('🎯 displayEnhancedSkillsInContainer called');
    console.log('Container:', container.length);
    console.log('Skills data:', skillsByCategory);

    if (!skillsByCategory || Object.keys(skillsByCategory).length === 0) {
        console.log('❌ No skills data available');
        container.html('<p class="text-gray-500 text-xs">No skills information available</p>');
        return;
    }

    console.log('✅ Processing skills categories:', Object.keys(skillsByCategory));

    let skillsHtml = '<div class="enhanced-skills-section">';

    Object.entries(skillsByCategory).forEach(([category, skills]) => {
        console.log(`Processing category: ${category} with ${skills.length} skills`);

        skillsHtml += `
            <div class="mb-4">
                <h6 class="font-semibold text-gray-800 text-sm mb-2 capitalize">
                    ${category.replace('_', ' ')} Skills (${skills.length})
                </h6>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        `;

        skills.forEach(skill => {
            console.log(`Processing skill: ${skill.name}`);
            const certifiedBadge = skill.certified ? '<span class="text-xs text-gray-600">✓ Certified</span>' : '';
            skillsHtml += `
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-2">
                    <div class="flex justify-between items-start">
                        <span class="font-medium text-gray-800 text-xs">${skill.name}</span>
                        <span class="text-xs text-gray-600 capitalize">${skill.proficiency}</span>
                    </div>
                    <div class="text-xs text-gray-600 mt-1">
                        ${skill.experience} years experience ${certifiedBadge}
                    </div>
                    ${skill.lastUsed ? `<div class="text-xs text-gray-500 mt-1">Last used: ${new Date(skill.lastUsed).toLocaleDateString()}</div>` : ''}
                </div>
            `;
        });

        skillsHtml += '</div></div>';
    });

    skillsHtml += '</div>';

    console.log('✅ Setting skills HTML');
    container.html(skillsHtml);
}

// Display enhanced achievements from API
function displayModalEnhancedAchievements(achievementsByCategory) {
    console.log('🔍 displayModalEnhancedAchievements called with:', achievementsByCategory);
    console.log('Achievement categories:', achievementsByCategory ? Object.keys(achievementsByCategory) : 'undefined');

    // Use the new achievements container
    const container = $('#modal-achievements-content');
    if (!container.length) {
        console.log('❌ Achievements container not found');
        return;
    }

    console.log('✅ Using achievements container:', container.length);
    displayEnhancedAchievementsInContainer(container, achievementsByCategory);
}

function displayEnhancedAchievementsInContainer(container, achievementsByCategory) {
    console.log('🎯 displayEnhancedAchievementsInContainer called');
    console.log('Container:', container.length);
    console.log('Achievements data:', achievementsByCategory);

    if (!achievementsByCategory || Object.keys(achievementsByCategory).length === 0) {
        console.log('❌ No achievements data available');
        container.html('<p class="text-gray-500 text-center">No achievements information available</p>');
        return;
    }

    console.log('✅ Processing achievements categories:', Object.keys(achievementsByCategory));

    let achievementsHtml = '<div class="enhanced-achievements-section space-y-6">';

    // Category configurations with colors and icons
    const categoryConfigs = {
        'software_development': {
            title: 'Software Development',
            icon: 'fas fa-code',
            color: 'blue',
            description: 'Technical projects and development achievements'
        },
        'teaching_excellence': {
            title: 'Teaching Excellence',
            icon: 'fas fa-chalkboard-teacher',
            color: 'green',
            description: 'Recognition for outstanding teaching performance'
        },
        'educational_innovation': {
            title: 'Educational Innovation',
            icon: 'fas fa-lightbulb',
            color: 'purple',
            description: 'Innovative approaches to education and learning'
        },
        'student_mentoring': {
            title: 'Student Mentoring',
            icon: 'fas fa-user-graduate',
            color: 'indigo',
            description: 'Student guidance and mentorship achievements'
        },
        'research_publication': {
            title: 'Research & Publications',
            icon: 'fas fa-book',
            color: 'teal',
            description: 'Academic research and published works'
        },
        'professional_recognition': {
            title: 'Professional Recognition',
            icon: 'fas fa-medal',
            color: 'yellow',
            description: 'Industry recognition and professional honors'
        },
        'community_service': {
            title: 'Community Service',
            icon: 'fas fa-hands-helping',
            color: 'pink',
            description: 'Community involvement and social impact'
        },
        'diversity_inclusion': {
            title: 'Diversity & Inclusion',
            icon: 'fas fa-users',
            color: 'red',
            description: 'Promoting diversity and inclusive practices'
        },
        'professional_certification': {
            title: 'Professional Certifications',
            icon: 'fas fa-certificate',
            color: 'orange',
            description: 'Professional certifications and credentials'
        },
        'entrepreneurship': {
            title: 'Entrepreneurship',
            icon: 'fas fa-rocket',
            color: 'cyan',
            description: 'Business ventures and entrepreneurial activities'
        },
        'intellectual_property': {
            title: 'Intellectual Property',
            icon: 'fas fa-brain',
            color: 'emerald',
            description: 'Patents, copyrights, and intellectual contributions'
        }
    };

    Object.entries(achievementsByCategory).forEach(([category, achievements]) => {
        console.log(`Processing category: ${category} with ${achievements.length} achievements`);

        const config = categoryConfigs[category] || {
            title: category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
            icon: 'fas fa-trophy',
            color: 'gray',
            description: 'Professional achievements and recognition'
        };

        achievementsHtml += `
            <div class="achievement-category mb-4">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                        <i class="${config.icon} text-gray-600 text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-bold text-gray-800 text-base">${config.title}</h4>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600 text-xs">${config.description}</p>
                            <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                ${achievements.length} achievement${achievements.length !== 1 ? 's' : ''}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        `;

        achievements.forEach(achievement => {
            console.log(`Processing achievement: ${achievement.title}`);

            // Skills display
            const skillsText = achievement.skills && achievement.skills.length > 0
                ? achievement.skills.slice(0, 3).join(', ') + (achievement.skills.length > 3 ? ` +${achievement.skills.length - 3} more` : '')
                : 'No specific skills listed';

            achievementsHtml += `
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow">
                    <div class="flex justify-between items-start mb-2">
                        <h5 class="font-semibold text-gray-800 text-xs leading-tight pr-2">${achievement.title}</h5>
                        <span class="inline-block bg-gray-100 text-gray-800 text-xs px-1.5 py-0.5 rounded-full flex-shrink-0">
                            ${achievement.level}
                        </span>
                    </div>

                    <p class="text-gray-700 text-xs mb-2 leading-relaxed line-clamp-2">${achievement.description}</p>

                    <div class="space-y-1 text-xs">
                        ${achievement.date ? `
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-calendar mr-1 w-3"></i>
                                <span>${new Date(achievement.date).toLocaleDateString()}</span>
                            </div>
                        ` : ''}

                        ${achievement.organization ? `
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-building mr-1 w-3"></i>
                                <span class="truncate">${achievement.organization}</span>
                            </div>
                        ` : ''}

                        ${achievement.impact ? `
                            <div class="flex items-start text-gray-600">
                                <i class="fas fa-chart-line mr-1 w-3 mt-0.5"></i>
                                <span class="leading-relaxed line-clamp-2">${achievement.impact}</span>
                            </div>
                        ` : ''}

                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-tools mr-1 w-3 mt-0.5"></i>
                            <span class="leading-relaxed line-clamp-1">${skillsText}</span>
                        </div>

                        ${achievement.evidenceUrl ? `
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-external-link-alt mr-1 w-3"></i>
                                <a href="${achievement.evidenceUrl}" target="_blank" class="text-gray-700 hover:text-gray-900 underline text-xs">
                                    Evidence
                                </a>
                            </div>
                        ` : ''}

                        ${achievement.amount && achievement.currency ? `
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-money-bill mr-1 w-3"></i>
                                <span>${achievement.currency} ${achievement.amount.toLocaleString()}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="flex justify-between items-center mt-2 pt-2 border-t border-gray-200">
                        <span class="text-xs text-gray-600">
                            ${achievement.verified ? '✅' : '⏳'}
                        </span>
                        <span class="text-xs text-gray-500 truncate">
                            ${config.title}
                        </span>
                    </div>
                </div>
            `;
        });

        achievementsHtml += '</div></div>';
    });

    achievementsHtml += '</div>';

    console.log('✅ Setting achievements HTML');
    container.html(achievementsHtml);
}
